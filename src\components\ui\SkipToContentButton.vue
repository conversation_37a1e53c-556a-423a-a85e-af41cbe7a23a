<script setup lang="ts">
import { Button } from "@/components/ui/button";

interface Props {
    targetId?: string;
    text?: string;
}

const props = withDefaults(defineProps<Props>(), {
    targetId: "main",
    text: "Skip to content",
});

const skipToContent = (event: Event) => {
    const target = document.getElementById(props.targetId);
    if (target) {
        // Hide the skip button immediately by removing focus
        (event.target as HTMLElement)?.blur();

        // Scroll to the main content area
        target.scrollIntoView({ behavior: "smooth", block: "start" });

        // Find the first focusable element within the main content
        const focusableElements = target.querySelectorAll(
            'a[href], button, input, textarea, select, details, [tabindex]:not([tabindex="-1"])'
        );

        if (focusableElements.length > 0) {
            // Focus the first focusable element
            (focusableElements[0] as HTMLElement).focus();
        } else {
            // If no focusable elements exist, make the main element focusable and focus it
            target.setAttribute("tabindex", "-1");
            target.focus();
            // Remove the tabindex after a short delay to avoid affecting normal tab flow
            setTimeout(() => {
                target.removeAttribute("tabindex");
            }, 100);
        }
    }
};
</script>

<template>
    <Button
        @click="skipToContent"
        @keydown.enter="skipToContent"
        @keydown.space.prevent="skipToContent"
        variant="outline"
        size="sm"
        class="sr-only focus:not-sr-only focus:absolute focus:top-2 focus:left-2 focus:z-50 bg-white border-slate-300 text-slate-800 shadow-lg hover:bg-slate-50 focus:ring-2 focus:ring-sky-600 focus:ring-offset-2"
        :aria-label="`${text} - Press Enter or Space to skip navigation`"
    >
        {{ text }}
    </Button>
</template>
