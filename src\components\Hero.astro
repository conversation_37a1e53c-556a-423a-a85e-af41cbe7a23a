---
import { Image } from 'astro:assets';
import type { PersonalInfo, ResearchField } from '@/data/portfolio';
import profilePlaceholder from '@/assets/images/profile-placeholder.svg';

interface Props {
  personalInfo: PersonalInfo;
  researchFields: ResearchField[];
}

const { personalInfo, researchFields } = Astro.props;
---

<section id="profile" class="pt-10 sm:pt-14 pb-10">
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
    <!-- Left Column: Large Hero Profile Image -->
    <div class="order-2 lg:order-1">
      <figure class="mx-auto w-[280px] h-[280px] sm:w-[320px] sm:h-[320px] lg:w-[400px] lg:h-[400px] rounded-2xl ring-4 ring-sky-800/80 ring-offset-4 ring-offset-slate-100 overflow-hidden bg-slate-100 flex items-center justify-center shadow-xl">
        <Image src={profilePlaceholder} alt={`Profile picture placeholder for ${personalInfo.name}`} width={400} height={400} class="object-cover w-full h-full" />
      </figure>
    </div>

    <!-- Right Column: Profile Information -->
    <div class="order-1 lg:order-2">
      <h1 class="text-3xl sm:text-4xl lg:text-5xl text-sky-900 font-extrabold leading-tight">{personalInfo.name}</h1>
      <p class="mt-2 text-lg text-slate-600 italic">{personalInfo.jobTitle}</p>

      <dl class="mt-6 grid grid-cols-1 gap-3" aria-label="Professional contact information">
        <div class="bg-white border border-slate-200 rounded-lg px-4 py-3 shadow-sm">
          <dt class="sr-only">Email</dt>
          <dd class="text-sm font-medium">Email: {personalInfo.email}</dd>
        </div>
        <div class="bg-white border border-slate-200 rounded-lg px-4 py-3 shadow-sm">
          <dt class="sr-only">Phone</dt>
          <dd class="text-sm font-medium">Phone: {personalInfo.phone}</dd>
        </div>
        <div class="bg-white border border-slate-200 rounded-lg px-4 py-3 shadow-sm">
          <dt class="sr-only">Address</dt>
          <dd class="text-sm font-medium">Address: {personalInfo.address}</dd>
        </div>
      </dl>

      <div class="mt-6">
        <h2 class="text-xl text-sky-900 font-bold">Research Fields</h2>
        <ul class="mt-3 flex flex-wrap gap-2" role="list" aria-label="Research specialization tags">
          {researchFields.map((field: ResearchField) => (
            <li>
              <span class="inline-block bg-sky-50 text-sky-900 border border-sky-200 px-4 py-2 rounded-full text-sm font-medium hover:bg-sky-100 transition-colors">
                {field.name}
              </span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  </div>
</section>
