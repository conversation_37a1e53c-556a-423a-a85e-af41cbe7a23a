<script setup lang="ts">
import { ref, computed } from 'vue'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent } from '@/components/ui/tabs'
import type { Publication } from '@/data/portfolio'

interface Props {
  publications: Publication[]
}

const props = defineProps<Props>()

const activeTab = ref('all')

const filteredPublications = computed(() => {
  switch (activeTab.value) {
    case 'published':
      return props.publications.filter(pub => pub.type === 'published')
    case 'unpublished':
      return props.publications.filter(pub => pub.type === 'unpublished')
    default:
      return props.publications
  }
})

const publishedCount = computed(() => 
  props.publications.filter(pub => pub.type === 'published').length
)

const unpublishedCount = computed(() => 
  props.publications.filter(pub => pub.type === 'unpublished').length
)

const getStatusText = computed(() => {
  switch (activeTab.value) {
    case 'published':
      return `Showing ${publishedCount.value} published paper${publishedCount.value !== 1 ? 's' : ''}.`
    case 'unpublished':
      return `Showing ${unpublishedCount.value} unpublished paper${unpublishedCount.value !== 1 ? 's' : ''}.`
    default:
      return `Showing all ${props.publications.length} paper${props.publications.length !== 1 ? 's' : ''}.`
  }
})
</script>

<template>
  <div class="mb-6">
    <p class="text-sm text-slate-500 mb-3">Filter:</p>
    
    <Tabs v-model="activeTab" class="w-full">
      <TabsList class="grid w-fit grid-cols-3 bg-slate-100 p-1">
        <TabsTrigger 
          value="all" 
          class="data-[state=active]:bg-white data-[state=active]:text-slate-900 text-slate-600 hover:text-slate-900 transition-colors"
        >
          All ({{ publications.length }})
        </TabsTrigger>
        <TabsTrigger 
          value="published"
          class="data-[state=active]:bg-white data-[state=active]:text-sky-700 text-slate-600 hover:text-slate-900 transition-colors"
        >
          Published ({{ publishedCount }})
        </TabsTrigger>
        <TabsTrigger 
          value="unpublished"
          class="data-[state=active]:bg-white data-[state=active]:text-amber-700 text-slate-600 hover:text-slate-900 transition-colors"
        >
          Unpublished ({{ unpublishedCount }})
        </TabsTrigger>
      </TabsList>
    </Tabs>
    
    <p class="text-sm text-slate-400 mt-3">{{ getStatusText }}</p>
  </div>

  <!-- Emit filtered publications for parent component to render -->
  <div style="display: none;">
    {{ $emit('filtered', filteredPublications) }}
  </div>
</template>

<script>
export default {
  emits: ['filtered']
}
</script>
